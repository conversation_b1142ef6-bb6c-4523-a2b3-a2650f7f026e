/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.logo h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0;
}

.logo span {
    font-size: 0.9rem;
    opacity: 0.9;
}

.nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.3s;
}

.nav a:hover {
    opacity: 0.8;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-box {
    position: relative;
}

.search-box input {
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    border: none;
    border-radius: 25px;
    width: 250px;
    font-family: 'Cairo', sans-serif;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.cart {
    position: relative;
    cursor: pointer;
    font-size: 1.2rem;
}

.cart-count {
    position: absolute;
    top: -8px;
    left: -8px;
    background: #fff;
    color: #ff6b9d;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #ffeef8, #f8e8ff);
    padding: 120px 0 80px;
    margin-top: 80px;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.hero-text h2 {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-text p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
}

.cta-button {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 30px;
    cursor: pointer;
    transition: transform 0.3s, box-shadow 0.3s;
    font-family: 'Cairo', sans-serif;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 157, 0.4);
}

.hero-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Section Titles */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    border-radius: 2px;
}

/* Categories Section */
.categories {
    padding: 80px 0;
    background: #fff;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.category-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.category-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.category-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 1rem;
}

.category-card p {
    color: #666;
    margin: 0 1rem 1.5rem;
    font-size: 0.9rem;
}

/* Featured Products */
.featured-products {
    padding: 80px 0;
    background: #f8f9fa;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 107, 157, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.add-to-cart {
    background: white;
    color: #ff6b9d;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s;
    font-family: 'Cairo', sans-serif;
}

.add-to-cart:hover {
    transform: scale(1.05);
}

.product-info {
    padding: 1.5rem;
}

.product-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.rating {
    margin-bottom: 1rem;
}

.rating i {
    color: #ffd700;
    font-size: 0.9rem;
}

.price {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.current-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: #ff6b9d;
}

.old-price {
    font-size: 1rem;
    color: #999;
    text-decoration: line-through;
}

/* Special Offers */
.special-offers {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.offer-banner {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.offer-content {
    padding: 3rem;
}

.offer-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.offer-content p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
}

.offer-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 30px;
    cursor: pointer;
    transition: transform 0.3s;
    font-family: 'Cairo', sans-serif;
}

.offer-button:hover {
    transform: translateY(-2px);
}

.offer-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

/* About Section */
.about {
    padding: 80px 0;
    background: #fff;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.about-text p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.about-text ul {
    list-style: none;
}

.about-text li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: #555;
}

.about-text li i {
    color: #ff6b9d;
    font-size: 1.2rem;
}

.about-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: #f8f9fa;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.contact-item i {
    font-size: 1.5rem;
    color: #ff6b9d;
    width: 40px;
    text-align: center;
}

.contact-item h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.contact-item p {
    color: #666;
    font-size: 1rem;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #ff6b9d;
}

.contact-form button {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 10px;
    cursor: pointer;
    transition: transform 0.3s;
    font-family: 'Cairo', sans-serif;
    width: 100%;
}

.contact-form button:hover {
    transform: translateY(-2px);
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ff6b9d;
}

.footer-section p {
    color: #bdc3c7;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: #ff6b9d;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #34495e;
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: background 0.3s, transform 0.3s;
}

.social-links a:hover {
    background: #ff6b9d;
    transform: translateY(-2px);
}

.newsletter {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.newsletter input {
    flex: 1;
    padding: 0.8rem;
    border: none;
    border-radius: 5px;
    font-family: 'Cairo', sans-serif;
}

.newsletter button {
    background: #ff6b9d;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 5px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    transition: background 0.3s;
}

.newsletter button:hover {
    background: #c44569;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .header-actions .search-box {
        display: none;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-text h2 {
        font-size: 2rem;
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .offer-banner {
        grid-template-columns: 1fr;
    }

    .about-content {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .newsletter {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-text h2 {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .categories-grid,
    .products-grid {
        grid-template-columns: 1fr;
    }

    .offer-content {
        padding: 2rem;
    }

    .offer-content h2 {
        font-size: 2rem;
    }
}
